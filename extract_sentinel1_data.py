#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
提取实测点的Sentinel-1A数据
Extract Sentinel-1A data for field measurement points

从ForestPoints.shp中的每个实测点提取所有可用的Sentinel-1A数据
Extract all available Sentinel-1A data from each point in ForestPoints.shp

Author: Generated for data validation
Date: 2025-07-29
"""

import ee
import pandas as pd
import geopandas as gpd
from datetime import datetime, timedelta
import pytz
import os
import numpy as np
import time
import warnings
warnings.filterwarnings('ignore')

# 初始化Google Earth Engine
try:
    ee.Initialize()
    print("✓ Google Earth Engine initialized successfully!")
except Exception as e:
    print(f"✗ Error initializing GEE: {e}")
    print("Please run 'earthengine authenticate' first")

class Sentinel1DataExtractor:
    """Sentinel-1A数据提取器"""
    
    def __init__(self, points_shapefile):
        """
        初始化
        
        Args:
            points_shapefile: 实测点shapefile路径
        """
        self.points_shapefile = points_shapefile
        self.points_gdf = None
        self.sentinel1_id = 'COPERNICUS/S1_GRD'
        
        # 北京时区
        self.beijing_tz = pytz.timezone('Asia/Shanghai')
        self.utc_tz = pytz.timezone('UTC')
        
    def load_points(self):
        """加载实测点数据"""
        try:
            self.points_gdf = gpd.read_file(self.points_shapefile)
            print(f"✓ 成功加载 {len(self.points_gdf)} 个实测点")
            print(f"✓ 坐标系: {self.points_gdf.crs}")
            
            # 显示点的基本信息
            print("\n实测点信息:")
            for idx, point in self.points_gdf.iterrows():
                geom = point.geometry
                print(f"  点 {idx+1}: 经度 {geom.x:.6f}, 纬度 {geom.y:.6f}")
            
            return True
        except Exception as e:
            print(f"✗ 加载实测点失败: {e}")
            return False
    
    def convert_to_beijing_time(self, timestamp_ms):
        """将时间戳转换为北京时间"""
        try:
            # 将毫秒时间戳转换为UTC时间
            utc_time = datetime.fromtimestamp(timestamp_ms / 1000, tz=self.utc_tz)
            # 转换为北京时间
            beijing_time = utc_time.astimezone(self.beijing_tz)
            return beijing_time.strftime('%Y-%m-%d %H:%M:%S')
        except Exception as e:
            print(f"时间转换错误: {e}")
            return "N/A"
    
    def get_sentinel1_data_for_point(self, lon, lat, point_id):
        """获取单个点的所有Sentinel-1A数据"""
        print(f"\n正在处理点 {point_id}: ({lon:.6f}, {lat:.6f})")
        
        # 创建点几何
        point_geom = ee.Geometry.Point([lon, lat])
        
        # 获取Sentinel-1A数据集
        # 从Sentinel-1A发射开始到现在的所有数据
        start_date = '2014-04-03'  # Sentinel-1A发射日期
        end_date = datetime.now().strftime('%Y-%m-%d')
        
        collection = ee.ImageCollection(self.sentinel1_id) \
            .filterDate(start_date, end_date) \
            .filterBounds(point_geom) \
            .filter(ee.Filter.listContains('transmitterReceiverPolarisation', 'VV'))
        
        # 获取集合信息
        collection_size = collection.size().getInfo()
        print(f"  找到 {collection_size} 个Sentinel-1A影像")
        
        if collection_size == 0:
            print(f"  警告: 点 {point_id} 没有找到Sentinel-1A数据")
            return pd.DataFrame()
        
        # 限制数量以避免超时（如果数据太多）
        if collection_size > 1000:
            print(f"  数据量较大，限制为最新的1000个影像")
            collection = collection.sort('system:time_start', False).limit(1000)
        
        # 提取数据
        data_list = []
        
        try:
            # 获取影像列表
            image_list = collection.toList(collection.size())
            
            # 分批处理以避免超时
            batch_size = 50
            total_images = collection.size().getInfo()
            
            for i in range(0, total_images, batch_size):
                print(f"  处理批次 {i//batch_size + 1}/{(total_images-1)//batch_size + 1}")
                
                batch_end = min(i + batch_size, total_images)
                batch_list = image_list.slice(i, batch_end)
                
                # 处理批次中的每个影像
                for j in range(batch_end - i):
                    try:
                        image = ee.Image(batch_list.get(j))
                        
                        # 获取影像属性
                        properties = image.getInfo()['properties']
                        
                        # 提取时间信息
                        timestamp = properties.get('system:time_start', 0)
                        beijing_time = self.convert_to_beijing_time(timestamp)
                        
                        # 提取轨道信息
                        orbit_number = properties.get('orbitNumber_start', 'N/A')
                        
                        # 提取入射角
                        angle_band = image.select('angle')
                        angle_value = angle_band.reduceRegion(
                            reducer=ee.Reducer.first(),
                            geometry=point_geom,
                            scale=10,
                            maxPixels=1
                        ).getInfo().get('angle', 'N/A')
                        
                        # 提取极化数据
                        # 检查可用的极化
                        polarizations = properties.get('transmitterReceiverPolarisation', [])
                        
                        vv_value = 'N/A'
                        hh_value = 'N/A'
                        vh_value = 'N/A'
                        hv_value = 'N/A'
                        
                        if 'VV' in polarizations:
                            vv_band = image.select('VV')
                            vv_result = vv_band.reduceRegion(
                                reducer=ee.Reducer.first(),
                                geometry=point_geom,
                                scale=10,
                                maxPixels=1
                            ).getInfo()
                            vv_value = vv_result.get('VV', 'N/A')
                        
                        if 'HH' in polarizations:
                            hh_band = image.select('HH')
                            hh_result = hh_band.reduceRegion(
                                reducer=ee.Reducer.first(),
                                geometry=point_geom,
                                scale=10,
                                maxPixels=1
                            ).getInfo()
                            hh_value = hh_result.get('HH', 'N/A')
                        
                        if 'VH' in polarizations:
                            vh_band = image.select('VH')
                            vh_result = vh_band.reduceRegion(
                                reducer=ee.Reducer.first(),
                                geometry=point_geom,
                                scale=10,
                                maxPixels=1
                            ).getInfo()
                            vh_value = vh_result.get('VH', 'N/A')
                        
                        if 'HV' in polarizations:
                            hv_band = image.select('HV')
                            hv_result = hv_band.reduceRegion(
                                reducer=ee.Reducer.first(),
                                geometry=point_geom,
                                scale=10,
                                maxPixels=1
                            ).getInfo()
                            hv_value = hv_result.get('HV', 'N/A')
                        
                        # 添加到数据列表
                        data_list.append({
                            '卫星时间': beijing_time,
                            '地图号': orbit_number,
                            '点经度': lon,
                            '点纬度': lat,
                            'VV极化值': vv_value,
                            'HH极化值': hh_value,
                            'VH极化值': vh_value,
                            'HV极化值': hv_value,
                            'angle': angle_value,
                            '极化模式': ','.join(polarizations) if polarizations else 'N/A'
                        })
                        
                    except Exception as e:
                        print(f"    处理影像 {j} 时出错: {e}")
                        continue
            
        except Exception as e:
            print(f"  提取数据时出错: {e}")
            return pd.DataFrame()
        
        # 创建DataFrame
        df = pd.DataFrame(data_list)
        
        if not df.empty:
            # 按时间排序
            df = df.sort_values('卫星时间')
            print(f"  ✓ 成功提取 {len(df)} 条记录")
        else:
            print(f"  ✗ 未能提取到有效数据")
        
        return df
    
    def extract_all_points(self):
        """提取所有点的Sentinel-1A数据"""
        if self.points_gdf is None:
            print("请先加载实测点数据")
            return False
        
        print(f"\n开始提取 {len(self.points_gdf)} 个点的Sentinel-1A数据...")
        
        # 创建输出目录
        output_dir = "sentinel1_data_tables"
        os.makedirs(output_dir, exist_ok=True)
        
        # 处理每个点
        for idx, point in self.points_gdf.iterrows():
            point_id = idx + 1
            geom = point.geometry
            lon, lat = geom.x, geom.y
            
            # 获取该点的数据
            df = self.get_sentinel1_data_for_point(lon, lat, point_id)
            
            if not df.empty:
                # 保存为CSV文件
                filename = f"point_{point_id}_sentinel1_data.csv"
                filepath = os.path.join(output_dir, filename)
                df.to_csv(filepath, index=False, encoding='utf-8-sig')
                print(f"  ✓ 数据已保存到: {filepath}")
                
                # 保存为Excel文件（更好的中文支持）
                excel_filename = f"point_{point_id}_sentinel1_data.xlsx"
                excel_filepath = os.path.join(output_dir, excel_filename)
                df.to_excel(excel_filepath, index=False, engine='openpyxl')
                print(f"  ✓ Excel文件已保存到: {excel_filepath}")
                
                # 显示数据摘要
                print(f"  数据摘要:")
                print(f"    - 总记录数: {len(df)}")
                print(f"    - 时间范围: {df['卫星时间'].min()} 到 {df['卫星时间'].max()}")
                print(f"    - 有效VV数据: {df['VV极化值'].notna().sum()}")
                print(f"    - 有效HH数据: {df['HH极化值'].notna().sum()}")
            else:
                print(f"  ✗ 点 {point_id} 没有有效数据")
        
        print(f"\n✓ 所有数据提取完成！结果保存在 {output_dir} 目录中")
        return True

def main():
    """主函数"""
    print("Sentinel-1A数据提取工具")
    print("=" * 50)
    
    # 设置实测点文件路径
    points_file = "./reaserch region/ForestPoints.shp"
    
    # 检查文件是否存在
    if not os.path.exists(points_file):
        print(f"✗ 文件不存在: {points_file}")
        return
    
    # 创建提取器
    extractor = Sentinel1DataExtractor(points_file)
    
    # 加载实测点
    if not extractor.load_points():
        return
    
    # 提取数据
    extractor.extract_all_points()

if __name__ == "__main__":
    main()
