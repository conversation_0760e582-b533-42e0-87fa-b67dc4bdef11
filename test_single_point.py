#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试单个点的Sentinel-1A数据提取
Test Sentinel-1A data extraction for a single point

用于测试修复后的代码是否正常工作
Test if the fixed code works properly

Author: Generated for testing
Date: 2025-07-29
"""

import ee
import pandas as pd
import geopandas as gpd
from datetime import datetime
import pytz
import os

def initialize_gee():
    """初始化GEE"""
    try:
        ee.Initialize()
        print("✓ Google Earth Engine 初始化成功!")
        return True
    except Exception as e:
        print(f"✗ GEE初始化失败: {e}")
        return False

def test_single_point():
    """测试单个点的数据提取"""
    print("测试单个点的Sentinel-1A数据提取...")
    
    # 从ForestPoints.shp读取第一个点
    try:
        gdf = gpd.read_file("./reaserch region/ForestPoints.shp")
        if len(gdf) == 0:
            print("✗ ForestPoints.shp中没有点数据")
            return False
        
        # 获取第一个点的坐标
        first_point = gdf.iloc[0]
        lon, lat = first_point.geometry.x, first_point.geometry.y
        print(f"测试点坐标: 经度 {lon:.6f}, 纬度 {lat:.6f}")
        
    except Exception as e:
        print(f"✗ 读取shapefile失败: {e}")
        return False
    
    # 创建点几何
    point = ee.Geometry.Point([lon, lat])
    
    # 获取一个Sentinel-1A影像进行测试
    try:
        collection = ee.ImageCollection('COPERNICUS/S1_GRD') \
            .filterDate('2023-01-01', '2023-12-31') \
            .filterBounds(point) \
            .first()
        
        if collection is None:
            print("✗ 没有找到测试影像")
            return False
        
        print("✓ 找到测试影像")
        
        # 测试不同的像素值提取方法
        print("\n测试像素值提取方法:")
        
        # 方法1: sample
        try:
            sample_result = collection.sample(
                region=point,
                scale=10,
                numPixels=1,
                dropNulls=False
            ).getInfo()
            
            if sample_result and len(sample_result) > 0:
                print("✓ sample方法成功")
                print(f"  可用波段: {list(sample_result[0]['properties'].keys())}")
            else:
                print("✗ sample方法失败")
        except Exception as e:
            print(f"✗ sample方法出错: {e}")
        
        # 方法2: reduceRegion
        try:
            reduce_result = collection.reduceRegion(
                reducer=ee.Reducer.first(),
                geometry=point,
                scale=10,
                maxPixels=1e9,
                bestEffort=True
            ).getInfo()
            
            if reduce_result:
                print("✓ reduceRegion方法成功")
                print(f"  可用波段: {list(reduce_result.keys())}")
            else:
                print("✗ reduceRegion方法失败")
        except Exception as e:
            print(f"✗ reduceRegion方法出错: {e}")
        
        return True
        
    except Exception as e:
        print(f"✗ 测试过程出错: {e}")
        return False

def test_batch_processing():
    """测试批量处理"""
    print("\n测试批量处理...")
    
    try:
        # 从ForestPoints.shp读取所有点
        gdf = gpd.read_file("./reaserch region/ForestPoints.shp")
        print(f"总共 {len(gdf)} 个点")
        
        # 测试前3个点（如果有的话）
        test_count = min(3, len(gdf))
        print(f"测试前 {test_count} 个点")
        
        for idx in range(test_count):
            point = gdf.iloc[idx]
            lon, lat = point.geometry.x, point.geometry.y
            print(f"\n点 {idx+1}: 经度 {lon:.6f}, 纬度 {lat:.6f}")
            
            # 检查该点是否有Sentinel-1A数据
            point_geom = ee.Geometry.Point([lon, lat])
            collection = ee.ImageCollection('COPERNICUS/S1_GRD') \
                .filterDate('2023-01-01', '2023-12-31') \
                .filterBounds(point_geom)
            
            count = collection.size().getInfo()
            print(f"  找到 {count} 个影像")
        
        return True
        
    except Exception as e:
        print(f"✗ 批量测试出错: {e}")
        return False

def main():
    """主函数"""
    print("=" * 50)
    print("Sentinel-1A数据提取测试")
    print("=" * 50)
    
    # 初始化GEE
    if not initialize_gee():
        return
    
    # 检查文件
    if not os.path.exists("./reaserch region/ForestPoints.shp"):
        print("✗ ForestPoints.shp文件不存在")
        return
    
    # 运行测试
    print("\n1. 测试单个点数据提取:")
    if test_single_point():
        print("✓ 单点测试通过")
    else:
        print("✗ 单点测试失败")
        return
    
    print("\n2. 测试批量处理:")
    if test_batch_processing():
        print("✓ 批量测试通过")
    else:
        print("✗ 批量测试失败")
        return
    
    print("\n" + "=" * 50)
    print("✅ 所有测试通过!")
    print("现在可以安全运行完整的验证程序")
    print("运行命令: python validate_sentinel1_points_fixed.py")
    print("或者: python run_validation.py")
    print("=" * 50)

if __name__ == "__main__":
    main()
