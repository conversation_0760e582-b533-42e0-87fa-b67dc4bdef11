# 基于GEE平台，结合Sentinel-1A、MODIS LAI和SMAP L4（SWI滤波）反演土壤水分的流程图

1.  **GEE 平台数据准备与筛选**

*   **研究区 SHP 加载**：数据在./reasersh region/Basins_1.shp，通过`ee.FeatureCollection`函数加载，作为数据筛选的空间边界。


*   **数据集调用**：



    *   **Sentinel-1A 数据**：调用 GEE 中的`COPERNICUS/S1_GRD`数据集，筛选 VV/VH 极化模式、轨道方向（如降轨）的数据，设置研究区边界和时间范围。


    *   **MODIS LAI 数据**：调用`MODIS/061/MOD15A2H`数据集，该数据集为 8 天合成产品，空间分辨率 500m。


    *   **SMAP L4 土壤水分数据**：调用`NASA/SMAP/SPL4SMGP/007`数据集，提取 0-10cm 土壤水分产品，空间分辨率 3km。


*   **有效数据筛选**：



    *   **同日成像筛选**：通过`filterDate`函数对三个数据集按时间维度筛选，遍历时间范围内的日期，对每一天，检查三个数据集在研究区内是否均有成像记录，筛选出同一天内三个数据集均覆盖研究区的日期，形成候选日期列表。


    *   **空间完整性检查**：对候选日期的每个数据集，通过`clip`函数裁剪至研究区，利用`reduceRegion`函数统计研究区内有效像元占比，若占比低于 80%（即存在部分区域无数据），则剔除该日期，保留研究区内数据完整的日期作为有效日期，对应数据为有效数据。


    *   **空间分辨率统一**：在 GEE 中通过重采样实现，将 Sentinel-1A 数据（原生 10m/20m）和 MODIS LAI 数据（500m）统一重采样至 SMAP L4 数据的 3km 分辨率，采用`reproject`函数，设置投影参数与 SMAP L4 一致。


1.  **GEE 平台数据预处理**

*   **Sentinel-1A 数据预处理**：



    *   在 GEE 中调用`preprocess`函数链，完成辐射定标（将 DN 值转换为后向散射系数）、去噪（采用内置 Lee 滤波算法），无需手动下载，直接在平台内处理。


    *   基于有效日期，提取对应日期的后向散射系数数据，裁剪至研究区。


*   **MODIS LAI 数据预处理**：



    *   利用`qualityMosaic`函数结合 QC 波段进行质量控制，保留可信度≥80% 的像元，剔除云污染区域。


    *   对有效日期的 LAI 数据，裁剪至研究区，并重采样至 3km 分辨率。


*   **SMAP L4 土壤水分数据 SWI 滤波处理**：



    *   在 GEE 中通过滑动窗口均值实现 SWI 滤波，基于研究区气候特征设置窗口大小（干旱区 15 天，湿润区 7 天），使用`movingWindow`函数对有效日期的土壤水分数据进行平滑处理，作为模型训练的 ground-truth。


1.  **水云模型参数拟合（基于 GEE 平台计算）**

*   **水云模型公式设定**：同之前的改进型水云模型，公式为$\sigma_{total}=\sigma_{veg}+\sigma_{soil} \cdot \exp(-2b \cdot LAI)$，其中$\sigma_{veg}=a \cdot LAI$，未知参数为$a$和$b$。


*   **参数拟合方法**：



    *   数据匹配：在 GEE 中通过`ee.Image.cat`将有效日期的$\sigma_{total}$（Sentinel-1A）、LAI（MODIS）、SMAP L4-SWI 土壤水分数据拼接，形成影像集合，通过`sampleRegions`函数在研究区随机采样（样本量≥500 个）。


    *   基于 Oh 模型的$\sigma_{soil}$估算：利用 SMAP L4-SWI 土壤水分（$\theta$）反推$\sigma_{soil}$，Oh 模型公式及参数同前，在 GEE 中通过`expression`函数实现计算。


    *   非线性最小二乘拟合：将采样数据导出至 GEE 客户端，使用 Python 的`scipy.optimize.curve_fit`函数求解$a$和$b$，目标函数为最小化$\vert\sigma_{total}-\hat{\sigma}_{total}\vert^2$。


    *   参数验证：选取 10% 样本进行交叉验证，若 RMSE≤1.5 dB 则接受参数，否则重新调整样本集（如剔除异常值）重复拟合。


1.  **土壤后向散射分离（GEE 平台实现）**

*   将拟合得到的$a$、$b$参数输入 GEE，通过`expression`函数代入水云模型公式，计算土壤后向散射系数$\sigma_{soil}=(\sigma_{total}-\sigma_{veg}) \cdot \exp(2b \cdot LAI)$，其中$\sigma_{veg}=a \cdot LAI$。


*   质量控制：在 GEE 中通过`updateMask`函数剔除 LAI>6 m²/m² 的像元和$\sigma_{soil}$不在 - 25 dB 至 - 5 dB 范围内的像元。


1.  **Oh 模型参数本地化（可选，GEE 辅助实现）**

*   若研究区土壤类型特殊，在 GEE 中提取分离后的$\sigma_{soil}$和对应的 SMAP L4-SWI 数据，采样形成样本对（$\sigma_{soil}$，$\theta_{SMAP}$），按土壤质地分层。


*   导出样本数据至本地，采用多元非线性回归方法优化 Oh 模型参数$p_1,p_2,p_3$，目标函数为最小化$\vert\theta_{SMAP}-\hat{\theta}\vert^2$。


1.  **土壤水分反演与验证（GEE 平台支持）**

*   反演流程：在 GEE 中利用优化后的水云模型分离的$\sigma_{soil}$，代入 Oh 模型（固定或本地化参数），通过`expression`函数计算土壤水分$\theta_{retrieval}$。


*   精度验证：



    *   将实地采样点数据上传至 GEE，通过`ee.FeatureCollection`加载，提取反演结果中对应采样点的$\theta_{retrieval}$值。


    *   在 GEE 中通过`reduceRegions`函数计算验证指标（R²、RMSE、Bias），若 RMSE<0.04 m³/m² 且 R²>0.6，模型可接受。


1.  **结果可视化与导出（GEE 平台）**

*   在 GEE 地图面板中可视化研究区土壤水分反演结果，采用`Map.addLayer`函数设置合适的配色方案和显示参数。


*   通过`Export.image.toDrive`函数将反演结果导出至 Google Drive，用于后续分析。


