#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复版本：验证实测点的Sentinel-1A数据匹配情况
Fixed version: Validate Sentinel-1A data matching for field measurement points

专门为ForestPoints.shp中的实测点提取Sentinel-1A数据
Specifically extract Sentinel-1A data for points in ForestPoints.shp

Author: Generated for data validation (Fixed version)
Date: 2025-07-29
"""

import ee
import pandas as pd
import geopandas as gpd
from datetime import datetime
import pytz
import os
import time

# 初始化Google Earth Engine
def initialize_gee():
    """初始化GEE"""
    try:
        ee.Initialize()
        print("✓ Google Earth Engine 初始化成功!")
        return True
    except Exception as e:
        print(f"✗ GEE初始化失败: {e}")
        print("请先运行: earthengine authenticate")
        return False

def load_forest_points(shapefile_path):
    """加载森林实测点数据"""
    try:
        gdf = gpd.read_file(shapefile_path)
        print(f"✓ 成功加载 {len(gdf)} 个实测点")
        
        # 显示点的基本信息
        print("\n实测点坐标信息:")
        for idx, point in gdf.iterrows():
            geom = point.geometry
            print(f"  点 {idx+1}: 经度 {geom.x:.6f}, 纬度 {geom.y:.6f}")
            
        return gdf
    except Exception as e:
        print(f"✗ 加载shapefile失败: {e}")
        return None

def convert_to_beijing_time(timestamp_ms):
    """将UTC时间戳转换为北京时间"""
    try:
        beijing_tz = pytz.timezone('Asia/Shanghai')
        utc_tz = pytz.timezone('UTC')
        
        # 转换时间戳
        utc_time = datetime.fromtimestamp(timestamp_ms / 1000, tz=utc_tz)
        beijing_time = utc_time.astimezone(beijing_tz)
        
        return beijing_time.strftime('%Y-%m-%d %H:%M:%S')
    except:
        return "时间转换失败"

def extract_pixel_value_safe(image, lon, lat, band_name):
    """安全地提取像素值"""
    try:
        # 方法1: 使用sample (推荐用于点数据)
        point = ee.Geometry.Point([lon, lat])
        sample = image.select(band_name).sample(
            region=point,
            scale=10,
            numPixels=1,
            dropNulls=False
        )
        
        sample_list = sample.getInfo()
        if sample_list and len(sample_list) > 0:
            return sample_list[0]['properties'].get(band_name, 'N/A')
        
        # 方法2: 如果sample失败，使用reduceRegion
        result = image.select(band_name).reduceRegion(
            reducer=ee.Reducer.first(),
            geometry=point,
            scale=10,
            maxPixels=1e9,
            bestEffort=True
        ).getInfo()
        
        return result.get(band_name, 'N/A')
        
    except Exception as e:
        print(f"    提取{band_name}波段失败: {e}")
        return 'N/A'

def extract_sentinel1_for_point(lon, lat, point_id):
    """为单个点提取Sentinel-1A数据"""
    print(f"\n正在处理点 {point_id}: 经度 {lon:.6f}, 纬度 {lat:.6f}")
    
    # 创建点几何
    point_geom = ee.Geometry.Point([lon, lat])
    
    # 获取Sentinel-1A数据集 (从发射至今的所有数据)
    collection = ee.ImageCollection('COPERNICUS/S1_GRD') \
        .filterDate('2014-04-03', datetime.now().strftime('%Y-%m-%d')) \
        .filterBounds(point_geom) \
        .sort('system:time_start')
    
    # 获取数据数量
    try:
        total_count = collection.size().getInfo()
        print(f"  找到 {total_count} 个Sentinel-1A影像")
        
        if total_count == 0:
            print(f"  ⚠️ 点 {point_id} 没有找到Sentinel-1A数据")
            return pd.DataFrame()
        
        # 如果数据太多，限制数量
        if total_count > 200:  # 减少限制数量以提高稳定性
            print(f"  数据量较大，限制为最新的200个影像")
            collection = collection.sort('system:time_start', False).limit(200)
            total_count = 200
        
    except Exception as e:
        print(f"  ✗ 获取数据数量失败: {e}")
        return pd.DataFrame()
    
    # 提取数据
    data_records = []
    
    try:
        # 获取影像列表
        image_list = collection.toList(total_count)
        
        # 分批处理以避免超时
        batch_size = 10  # 减小批次大小
        
        for start_idx in range(0, total_count, batch_size):
            end_idx = min(start_idx + batch_size, total_count)
            print(f"  处理批次: {start_idx+1}-{end_idx}/{total_count}")
            
            for i in range(start_idx, end_idx):
                try:
                    # 获取单个影像
                    image = ee.Image(image_list.get(i))
                    
                    # 获取影像属性
                    props = image.getInfo()['properties']
                    
                    # 提取基本信息
                    timestamp = props.get('system:time_start', 0)
                    beijing_time = convert_to_beijing_time(timestamp)
                    orbit_number = props.get('orbitNumber_start', 'N/A')
                    
                    # 获取极化信息
                    polarizations = props.get('transmitterReceiverPolarisation', [])
                    
                    # 安全地提取各波段值
                    vv_value = extract_pixel_value_safe(image, lon, lat, 'VV') if 'VV' in polarizations else 'N/A'
                    hh_value = extract_pixel_value_safe(image, lon, lat, 'HH') if 'HH' in polarizations else 'N/A'
                    vh_value = extract_pixel_value_safe(image, lon, lat, 'VH') if 'VH' in polarizations else 'N/A'
                    hv_value = extract_pixel_value_safe(image, lon, lat, 'HV') if 'HV' in polarizations else 'N/A'
                    angle_value = extract_pixel_value_safe(image, lon, lat, 'angle')
                    
                    # 记录数据
                    record = {
                        '卫星时间': beijing_time,
                        '地图号': orbit_number,
                        '点经度': lon,
                        '点纬度': lat,
                        'VV极化值': vv_value,
                        'HH极化值': hh_value,
                        'VH极化值': vh_value,
                        'HV极化值': hv_value,
                        'angle': angle_value,
                        '极化模式': ','.join(polarizations) if polarizations else 'N/A'
                    }
                    
                    data_records.append(record)
                    print(f"    ✓ 影像 {i+1}/{total_count} 处理完成")
                    
                except Exception as e:
                    print(f"    ✗ 处理影像 {i+1} 时出错: {e}")
                    continue
            
            # 添加延迟以避免API限制
            time.sleep(2)
        
    except Exception as e:
        print(f"  ✗ 数据提取过程出错: {e}")
        return pd.DataFrame()
    
    # 创建DataFrame
    if data_records:
        df = pd.DataFrame(data_records)
        df = df.sort_values('卫星时间')
        print(f"  ✓ 成功提取 {len(df)} 条记录")
        
        # 显示数据统计
        vv_count = df[df['VV极化值'] != 'N/A'].shape[0]
        hh_count = df[df['HH极化值'] != 'N/A'].shape[0]
        vh_count = df[df['VH极化值'] != 'N/A'].shape[0]
        print(f"    - 有效VV数据: {vv_count} 条")
        print(f"    - 有效HH数据: {hh_count} 条")
        print(f"    - 有效VH数据: {vh_count} 条")
        
        return df
    else:
        print(f"  ✗ 未能提取到有效数据")
        return pd.DataFrame()

def save_point_data(df, point_id, output_dir):
    """保存单个点的数据"""
    if df.empty:
        return False
    
    # 创建文件名
    csv_filename = f"点{point_id}_Sentinel1A数据.csv"
    excel_filename = f"点{point_id}_Sentinel1A数据.xlsx"
    
    csv_path = os.path.join(output_dir, csv_filename)
    excel_path = os.path.join(output_dir, excel_filename)
    
    try:
        # 保存CSV文件
        df.to_csv(csv_path, index=False, encoding='utf-8-sig')
        
        # 保存Excel文件
        df.to_excel(excel_path, index=False, engine='openpyxl')
        
        print(f"  ✓ 数据已保存:")
        print(f"    - CSV: {csv_path}")
        print(f"    - Excel: {excel_path}")
        
        return True
    except Exception as e:
        print(f"  ✗ 保存文件失败: {e}")
        return False

def generate_summary_report(all_results, output_dir):
    """生成汇总报告"""
    summary_data = []
    
    for point_id, df in all_results.items():
        if not df.empty:
            summary_data.append({
                '点编号': point_id,
                '经度': df['点经度'].iloc[0],
                '纬度': df['点纬度'].iloc[0],
                '总记录数': len(df),
                '有效VV数据': df[df['VV极化值'] != 'N/A'].shape[0],
                '有效HH数据': df[df['HH极化值'] != 'N/A'].shape[0],
                '有效VH数据': df[df['VH极化值'] != 'N/A'].shape[0],
                '最早时间': df['卫星时间'].min(),
                '最晚时间': df['卫星时间'].max()
            })
        else:
            summary_data.append({
                '点编号': point_id,
                '经度': 'N/A',
                '纬度': 'N/A',
                '总记录数': 0,
                '有效VV数据': 0,
                '有效HH数据': 0,
                '有效VH数据': 0,
                '最早时间': 'N/A',
                '最晚时间': 'N/A'
            })
    
    summary_df = pd.DataFrame(summary_data)
    
    # 保存汇总报告
    summary_path = os.path.join(output_dir, "数据匹配汇总报告.xlsx")
    summary_df.to_excel(summary_path, index=False, engine='openpyxl')
    
    print(f"\n✓ 汇总报告已保存: {summary_path}")
    return summary_df

def main():
    """主函数"""
    print("=" * 60)
    print("实测点Sentinel-1A数据验证工具 (修复版本)")
    print("Field Points Sentinel-1A Data Validation Tool (Fixed)")
    print("=" * 60)
    
    # 初始化GEE
    if not initialize_gee():
        return
    
    # 设置文件路径
    points_file = "./reaserch region/ForestPoints.shp"
    
    # 检查文件
    if not os.path.exists(points_file):
        print(f"✗ 文件不存在: {points_file}")
        return
    
    # 加载实测点
    points_gdf = load_forest_points(points_file)
    if points_gdf is None:
        return
    
    # 创建输出目录
    output_dir = "实测点Sentinel1A数据验证结果_修复版"
    os.makedirs(output_dir, exist_ok=True)
    print(f"\n输出目录: {output_dir}")
    
    # 处理每个点
    all_results = {}
    
    for idx, point in points_gdf.iterrows():
        point_id = idx + 1
        geom = point.geometry
        lon, lat = geom.x, geom.y
        
        # 提取数据
        df = extract_sentinel1_for_point(lon, lat, point_id)
        all_results[point_id] = df
        
        # 保存数据
        if not df.empty:
            save_point_data(df, point_id, output_dir)
        
        print(f"  点 {point_id} 处理完成\n")
    
    # 生成汇总报告
    generate_summary_report(all_results, output_dir)
    
    # 显示最终统计
    print("\n" + "=" * 60)
    print("数据验证完成!")
    print("=" * 60)
    
    total_points = len(points_gdf)
    successful_points = sum(1 for df in all_results.values() if not df.empty)
    
    print(f"总实测点数: {total_points}")
    print(f"有数据的点: {successful_points}")
    print(f"无数据的点: {total_points - successful_points}")
    
    if successful_points > 0:
        total_records = sum(len(df) for df in all_results.values() if not df.empty)
        print(f"总数据记录: {total_records}")
        print(f"平均每点记录数: {total_records/successful_points:.1f}")
    
    print(f"\n所有结果已保存在目录: {output_dir}")

if __name__ == "__main__":
    main()
