# 实测点Sentinel-1A数据验证说明
# Field Points Sentinel-1A Data Validation Guide

## 📋 功能概述

本工具专门用于验证ForestPoints.shp中实测数据点与Sentinel-1A卫星数据的匹配情况。系统会为每个实测点提取从Sentinel-1A卫星发射至今的所有可用数据，并以表格形式输出详细信息。

## 🎯 输出内容

对于每个实测点，系统将生成包含以下字段的数据表格：

| 字段名 | 说明 | 示例 |
|--------|------|------|
| 卫星时间 | 卫星过境时间（北京时间） | 2023-07-15 14:23:45 |
| 地图号 | 卫星轨道编号 | 12345 |
| 点经度 | 实测点经度 | 116.123456 |
| 点纬度 | 实测点纬度 | 39.987654 |
| VV极化值 | VV极化后向散射系数 (dB) | -12.34 |
| HH极化值 | HH极化后向散射系数 (dB) | -15.67 |
| VH极化值 | VH极化后向散射系数 (dB) | -18.90 |
| HV极化值 | HV极化后向散射系数 (dB) | -20.12 |
| angle | 入射角 (度) | 35.2 |
| 极化模式 | 可用的极化组合 | VV,VH |

## 📁 文件结构

```
项目目录/
├── reaserch region/
│   ├── ForestPoints.shp          # 实测点数据 (必需)
│   ├── ForestPoints.dbf          # 属性数据 (必需)
│   ├── ForestPoints.shx          # 索引文件 (必需)
│   └── ForestPoints.prj          # 投影信息 (可选)
├── validate_sentinel1_points.py  # 主验证脚本
├── run_validation.py             # 快速启动脚本
└── 实测点数据验证说明.md         # 本说明文档
```

## 🚀 快速开始

### 方法1: 使用快速启动脚本 (推荐)

```bash
python run_validation.py
```

这个脚本会自动检查系统要求、文件完整性和GEE认证状态，然后运行验证程序。

### 方法2: 直接运行验证脚本

```bash
python validate_sentinel1_points.py
```

## 📦 系统要求

### Python包依赖

```bash
pip install earthengine-api pandas geopandas pytz openpyxl
```

### Google Earth Engine设置

1. 注册GEE账户: https://earthengine.google.com/
2. 安装Earth Engine API: `pip install earthengine-api`
3. 认证: `earthengine authenticate`

## 📊 输出结果

运行完成后，系统会在 `实测点Sentinel1A数据验证结果` 目录中生成以下文件：

### 单点数据文件
- `点1_Sentinel1A数据.csv` - CSV格式数据表
- `点1_Sentinel1A数据.xlsx` - Excel格式数据表
- `点2_Sentinel1A数据.csv` - 第二个点的数据
- ... (每个点一套文件)

### 汇总报告
- `数据匹配汇总报告.xlsx` - 所有点的数据统计汇总

汇总报告包含：
- 每个点的坐标信息
- 总数据记录数
- 有效VV/HH数据数量
- 数据时间范围

## 🔧 高级配置

### 修改数据时间范围

在 `validate_sentinel1_points.py` 中修改：

```python
# 默认从Sentinel-1A发射开始
collection = ee.ImageCollection('COPERNICUS/S1_GRD') \
    .filterDate('2014-04-03', datetime.now().strftime('%Y-%m-%d'))

# 修改为自定义时间范围
collection = ee.ImageCollection('COPERNICUS/S1_GRD') \
    .filterDate('2020-01-01', '2023-12-31')
```

### 调整数据数量限制

```python
# 默认限制为500个影像
if total_count > 500:
    collection = collection.sort('system:time_start', False).limit(500)

# 修改限制数量
if total_count > 1000:
    collection = collection.sort('system:time_start', False).limit(1000)
```

### 修改批处理大小

```python
# 默认批处理大小为25
batch_size = 25

# 调整为更大的批次 (注意可能导致超时)
batch_size = 50
```

## ⚠️ 注意事项

1. **数据量限制**: 如果某个点的Sentinel-1A数据过多，系统会自动限制为最新的500个影像以避免超时

2. **处理时间**: 每个点的处理时间取决于可用数据量，通常需要几分钟到十几分钟

3. **网络连接**: 需要稳定的网络连接访问Google Earth Engine服务

4. **API限制**: Google Earth Engine有API调用频率限制，如果遇到错误请稍后重试

5. **坐标系统**: 确保ForestPoints.shp使用WGS84坐标系 (EPSG:4326)

## 🐛 常见问题

### Q: 提示"GEE初始化失败"
A: 请运行 `earthengine authenticate` 进行认证

### Q: 某些点没有找到数据
A: 可能原因：
- 点位置在海洋或极地区域
- 时间范围内该区域没有Sentinel-1A覆盖
- 坐标系统不正确

### Q: 处理速度很慢
A: 可以：
- 减少批处理大小
- 缩短时间范围
- 减少数据数量限制

### Q: 出现"N/A"值
A: 表示该时间点该极化数据不可用，这是正常现象

## 📈 数据解读

### 极化模式说明
- **VV**: 垂直发射-垂直接收
- **VH**: 垂直发射-水平接收  
- **HH**: 水平发射-水平接收
- **HV**: 水平发射-垂直接收

### 后向散射系数
- 单位: dB (分贝)
- 范围: 通常在 -30 到 0 dB之间
- 值越大表示后向散射越强

### 入射角
- 单位: 度
- 范围: 通常在 20° 到 45° 之间
- 影响后向散射强度

## 📞 技术支持

如遇到问题，请检查：
1. 网络连接是否正常
2. GEE认证是否有效
3. 输入文件是否完整
4. Python环境是否正确配置

更多技术细节请参考Google Earth Engine官方文档：
https://developers.google.com/earth-engine/
