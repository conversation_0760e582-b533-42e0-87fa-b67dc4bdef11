#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速运行实测点Sentinel-1A数据验证
Quick run script for field points Sentinel-1A data validation

Author: Generated for data validation
Date: 2025-07-29
"""

import os
import sys

def check_requirements():
    """检查必要的依赖"""
    print("检查系统要求...")
    
    required_packages = {
        'ee': 'earthengine-api',
        'pandas': 'pandas',
        'geopandas': 'geopandas', 
        'pytz': 'pytz',
        'openpyxl': 'openpyxl'
    }
    
    missing_packages = []
    
    for package, install_name in required_packages.items():
        try:
            __import__(package)
            print(f"{package}")
        except ImportError:
            print(f"{package} (缺失)")
            missing_packages.append(install_name)
    
    if missing_packages:
        print(f"\n请安装缺失的包:")
        print(f"pip install {' '.join(missing_packages)}")
        return False
    
    return True

def check_files():
    """检查必要的文件"""
    print("\n检查必要文件...")
    
    required_files = [
        "./reaserch region/ForestPoints.shp",
        "./reaserch region/ForestPoints.dbf",
        "./reaserch region/ForestPoints.shx"
    ]
    
    missing_files = []
    
    for file_path in required_files:
        if os.path.exists(file_path):
            print(f"✓ {file_path}")
        else:
            print(f"✗ {file_path} (缺失)")
            missing_files.append(file_path)
    
    if missing_files:
        print(f"\n缺失文件:")
        for file_path in missing_files:
            print(f"  - {file_path}")
        return False
    
    return True

def check_gee_auth():
    """检查GEE认证"""
    print("\n检查Google Earth Engine认证...")
    
    try:
        import ee
        ee.Initialize()
        print("✓ Google Earth Engine认证成功")
        return True
    except Exception as e:
        print(f"✗ Google Earth Engine认证失败: {e}")
        print("\n请运行以下命令进行认证:")
        print("earthengine authenticate")
        return False

def main():
    """主函数"""
    print("=" * 60)
    print("实测点Sentinel-1A数据验证 - 快速启动")
    print("Field Points Sentinel-1A Data Validation - Quick Start")
    print("=" * 60)
    
    # 检查系统要求
    if not check_requirements():
        print("\n❌ 系统要求检查失败，请安装必要的依赖包")
        return False
    
    # 检查文件
    if not check_files():
        print("\n❌ 文件检查失败，请确保ForestPoints.shp文件存在")
        return False
    
    # 检查GEE认证
    if not check_gee_auth():
        print("\n❌ Google Earth Engine认证失败")
        return False
    
    print("\n✅ 所有检查通过!")
    print("\n开始运行数据验证...")
    print("=" * 60)
    
    # 运行验证脚本
    try:
        from validate_sentinel1_points import main as validate_main
        validate_main()
        
        print("\n" + "=" * 60)
        print("✅ 数据验证完成!")
        print("请查看输出目录中的结果文件")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"\n❌ 运行过程中出错: {e}")
        return False

if __name__ == "__main__":
    success = main()
    
    if not success:
        print("\n程序执行失败，请检查错误信息并重试")
        sys.exit(1)
    else:
        print("\n程序执行成功!")
        
        # 询问是否打开结果目录
        try:
            response = input("\n是否打开结果目录? (y/n): ").strip().lower()
            if response in ['y', 'yes', '是']:
                import subprocess
                import platform
                
                result_dir = "实测点Sentinel1A数据验证结果"
                
                if platform.system() == "Windows":
                    subprocess.run(f'explorer "{result_dir}"', shell=True)
                elif platform.system() == "Darwin":  # macOS
                    subprocess.run(f'open "{result_dir}"', shell=True)
                else:  # Linux
                    subprocess.run(f'xdg-open "{result_dir}"', shell=True)
                    
        except KeyboardInterrupt:
            print("\n程序结束")
        except Exception as e:
            print(f"\n打开目录失败: {e}")
            print(f"请手动打开目录: 实测点Sentinel1A数据验证结果")
